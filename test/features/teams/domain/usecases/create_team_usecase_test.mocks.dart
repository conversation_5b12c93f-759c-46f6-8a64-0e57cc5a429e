// Mocks generated by Mockito 5.4.6 from annotations
// in nextsportz_v2/test/features/teams/domain/usecases/create_team_usecase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart' as _i2;
import 'package:nextsportz_v2/features/teams/domain/repositories/teams_repository.dart'
    as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeTeam_0 extends _i1.SmartFake implements _i2.Team {
  _FakeTeam_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [TeamsRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockTeamsRepository extends _i1.Mock implements _i3.TeamsRepository {
  MockTeamsRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<List<_i2.Team>> getMyTeams() =>
      (super.noSuchMethod(
            Invocation.method(#getMyTeams, []),
            returnValue: _i4.Future<List<_i2.Team>>.value(<_i2.Team>[]),
          )
          as _i4.Future<List<_i2.Team>>);

  @override
  _i4.Future<_i2.Team> getTeamById(String? teamId) =>
      (super.noSuchMethod(
            Invocation.method(#getTeamById, [teamId]),
            returnValue: _i4.Future<_i2.Team>.value(
              _FakeTeam_0(this, Invocation.method(#getTeamById, [teamId])),
            ),
          )
          as _i4.Future<_i2.Team>);

  @override
  _i4.Future<_i2.Team> createTeam({
    required String? name,
    required String? description,
    String? logo,
    String? slogan,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createTeam, [], {
              #name: name,
              #description: description,
              #logo: logo,
              #slogan: slogan,
            }),
            returnValue: _i4.Future<_i2.Team>.value(
              _FakeTeam_0(
                this,
                Invocation.method(#createTeam, [], {
                  #name: name,
                  #description: description,
                  #logo: logo,
                  #slogan: slogan,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.Team>);

  @override
  _i4.Future<_i2.Team> updateTeam({
    required String? teamId,
    String? name,
    String? description,
    String? logo,
    String? slogan,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateTeam, [], {
              #teamId: teamId,
              #name: name,
              #description: description,
              #logo: logo,
              #slogan: slogan,
            }),
            returnValue: _i4.Future<_i2.Team>.value(
              _FakeTeam_0(
                this,
                Invocation.method(#updateTeam, [], {
                  #teamId: teamId,
                  #name: name,
                  #description: description,
                  #logo: logo,
                  #slogan: slogan,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.Team>);

  @override
  _i4.Future<void> deleteTeam(String? teamId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteTeam, [teamId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> invitePlayer({
    required String? teamId,
    required String? playerId,
    String? message,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#invitePlayer, [], {
              #teamId: teamId,
              #playerId: playerId,
              #message: message,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> acceptInvitation(String? invitationId) =>
      (super.noSuchMethod(
            Invocation.method(#acceptInvitation, [invitationId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> declineInvitation(String? invitationId) =>
      (super.noSuchMethod(
            Invocation.method(#declineInvitation, [invitationId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> removeMember({
    required String? teamId,
    required String? memberId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#removeMember, [], {
              #teamId: teamId,
              #memberId: memberId,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> updateMemberRole({
    required String? teamId,
    required String? memberId,
    required String? role,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateMemberRole, [], {
              #teamId: teamId,
              #memberId: memberId,
              #role: role,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<_i2.TeamInvitation>> getPendingInvitations() =>
      (super.noSuchMethod(
            Invocation.method(#getPendingInvitations, []),
            returnValue: _i4.Future<List<_i2.TeamInvitation>>.value(
              <_i2.TeamInvitation>[],
            ),
          )
          as _i4.Future<List<_i2.TeamInvitation>>);
}
