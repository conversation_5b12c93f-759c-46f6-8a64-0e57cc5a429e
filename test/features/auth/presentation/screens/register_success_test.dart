import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';
import 'package:nextsportz_v2/features/auth/domain/repositories/auth_repository.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/controller.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/auth_state.dart';
import 'package:nextsportz_v2/features/auth/presentation/screens/register_screen.dart';
import 'package:nextsportz_v2/features/auth/presentation/screens/otp_verification_screen.dart';
import 'package:nextsportz_v2/core/widgets/blaze_text_form_field.dart';
import 'package:nextsportz_v2/core/networking/app_error.dart';
import 'package:nextsportz_v2/features/auth/domain/entities/user.dart';
import 'register_success_test.mocks.dart';

@GenerateMocks([AuthRepository])
void main() {
  setUpAll(() {
    // Provide dummy values for Either types
    provideDummy<Either<AppError, void>>(const Right(null));
    provideDummy<Either<AppError, User>>(Right(User(
      id: 'test',
      name: 'Test User',
      email: '<EMAIL>',
      phoneNumber: '**********',
      role: 'PLAYER',
      createdAt: DateTime(2023),
      updatedAt: DateTime(2023),
      isActive: true,
    )));
  });

  group('Registration Success Flow Test', () {
    late MockAuthRepository mockAuthRepository;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
    });

    testWidgets(
        'should show success toast and navigate to OTP screen on successful registration',
        (tester) async {
      // Track navigation
      String? navigatedRoute;
      Map<String, String>? queryParams;

      // Mock successful registration
      when(mockAuthRepository.register(
        name: anyNamed('name'),
        email: anyNamed('email'),
        phoneNumber: anyNamed('phoneNumber'),
        password: anyNamed('password'),
        role: anyNamed('role'),
      )).thenAnswer((_) async => const Right(null));

      // Create a router for testing
      final router = GoRouter(
        initialLocation: '/register',
        routes: [
          GoRoute(
            path: '/register',
            builder: (context, state) => const RegisterScreen(),
          ),
          GoRoute(
            path: '/otp-verification',
            builder: (context, state) {
              navigatedRoute = '/otp-verification';
              queryParams = state.uri.queryParameters;
              return OtpVerificationScreen(
                phoneNumber: state.uri.queryParameters['phoneNumber'] ?? '',
                role: state.uri.queryParameters['role'] ?? 'PLAYER',
              );
            },
          ),
        ],
      );

      // Create the app with mocked repository
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            authNotifierProvider
                .overrideWith((ref) => AuthNotifier(mockAuthRepository)),
          ],
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      // Wait for the screen to load
      await tester.pumpAndSettle();

      // Verify we're on the register screen
      expect(find.text('Create account'), findsOneWidget);

      // Fill out the form
      final nameFields = find.byType(BlazeTextFormField);
      expect(nameFields, findsNWidgets(4)); // name, email, phone, password

      await tester.enterText(nameFields.at(0), 'John Doe');
      await tester.enterText(nameFields.at(1), '<EMAIL>');
      await tester.enterText(nameFields.at(2), '**********');
      await tester.enterText(nameFields.at(3), 'password123');

      // Submit the form
      await tester.tap(find.byType(FilledButton));
      await tester.pumpAndSettle();

      // Verify the repository method was called with correct parameters
      verify(mockAuthRepository.register(
        name: 'John Doe',
        email: '<EMAIL>',
        phoneNumber: '**********',
        password: 'password123',
        role: 'PLAYER',
      )).called(1);

      // Verify navigation occurred
      expect(navigatedRoute, equals('/otp-verification'));
      expect(queryParams?['phoneNumber'], equals('**********'));
      expect(queryParams?['role'], equals('PLAYER'));

      // Verify we're now on the OTP screen
      expect(find.byType(OtpVerificationScreen), findsOneWidget);
      expect(find.text('Verify Your Phone Number'), findsOneWidget);

      // Verify success message was shown (it might have disappeared by now)
      // We can't easily test for the SnackBar since it's transient

      print('✅ Registration success flow test passed');
      print('🧭 Navigated to: $navigatedRoute');
      print('📱 Phone: ${queryParams?['phoneNumber']}');
      print('👤 Role: ${queryParams?['role']}');
    });

    testWidgets(
        'should show error message and stay on register screen on failed registration',
        (tester) async {
      // Mock failed registration
      when(mockAuthRepository.register(
        name: anyNamed('name'),
        email: anyNamed('email'),
        phoneNumber: anyNamed('phoneNumber'),
        password: anyNamed('password'),
        role: anyNamed('role'),
      )).thenAnswer((_) async => const Left(AppError('Registration failed')));

      // Create the app with mocked repository
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            authNotifierProvider
                .overrideWith((ref) => AuthNotifier(mockAuthRepository)),
          ],
          child: MaterialApp(
            home: const RegisterScreen(),
          ),
        ),
      );

      // Wait for the screen to load
      await tester.pumpAndSettle();

      // Fill out the form
      final nameFields = find.byType(BlazeTextFormField);
      await tester.enterText(nameFields.at(0), 'John Doe');
      await tester.enterText(nameFields.at(1), '<EMAIL>');
      await tester.enterText(nameFields.at(2), '**********');
      await tester.enterText(nameFields.at(3), 'password123');

      // Submit the form
      await tester.tap(find.byType(FilledButton));
      await tester.pumpAndSettle();

      // Verify we're still on the register screen
      expect(find.text('Create account'), findsOneWidget);
      expect(find.byType(RegisterScreen), findsOneWidget);

      // Verify error message is shown
      expect(find.text('Registration failed'), findsOneWidget);
      expect(find.byType(SnackBar), findsOneWidget);

      print('✅ Registration failure flow test passed');
    });
  });
}
