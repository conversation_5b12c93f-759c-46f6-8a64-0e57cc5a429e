import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/controller.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/auth_state.dart';
import 'package:nextsportz_v2/features/auth/presentation/screens/register_screen.dart';
import 'package:nextsportz_v2/features/auth/presentation/screens/otp_verification_screen.dart';
import 'package:nextsportz_v2/core/widgets/blaze_text_form_field.dart';

import '../../../../test_helpers.dart';
import 'register_screen_test.mocks.dart';

@GenerateNiceMocks([MockSpec<AuthNotifier>()])
void main() {
  late MockAuthNotifier mockAuthNotifier;

  setUp(() {
    mockAuthNotifier = MockAuthNotifier();
    when(mockAuthNotifier.state).thenReturn(AuthState.initial());
    // Setup default stubs for addListener
    when(
      mockAuthNotifier.addListener(
        any,
        fireImmediately: anyNamed('fireImmediately'),
      ),
    ).thenReturn(() {});
  });

  group('RegisterScreen UI Tests', () {
    testWidgets('should render registration form with all fields', (
      tester,
    ) async {
      // Arrange
      when(mockAuthNotifier.state).thenReturn(AuthState.initial());

      // Act
      await tester.pumpWidget(
        TestHelpers.createTestApp(
          overrides: [
            authNotifierProvider.overrideWith((ref) => mockAuthNotifier),
          ],
          child: const RegisterScreen(),
        ),
      );

      // Assert
      expect(find.text('Create account'), findsAtLeastNWidgets(1));
      expect(
        find.byType(BlazeTextFormField),
        findsNWidgets(4),
      ); // name, email, phone, password
      expect(
        find.byType(DropdownButtonFormField),
        findsOneWidget,
      ); // role selector
      expect(find.byType(FilledButton), findsOneWidget); // submit button
    });

    testWidgets('should show validation errors for empty fields', (
      tester,
    ) async {
      // Arrange
      when(mockAuthNotifier.state).thenReturn(AuthState.initial());

      // Act
      await tester.pumpWidget(
        TestHelpers.createTestApp(
          overrides: [
            authNotifierProvider.overrideWith((ref) => mockAuthNotifier),
          ],
          child: const RegisterScreen(),
        ),
      );

      // Tap submit button without filling fields
      await tester.tap(find.byType(FilledButton));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Enter name'), findsOneWidget);
      expect(find.text('Enter email'), findsOneWidget);
      expect(find.text('Enter phone'), findsOneWidget);
      expect(find.text('Min 6 chars'), findsOneWidget);
    });

    testWidgets('should call register method with correct parameters', (
      tester,
    ) async {
      // Arrange
      when(mockAuthNotifier.state).thenReturn(AuthState.initial());
      when(
        mockAuthNotifier.register(
          name: anyNamed('name'),
          email: anyNamed('email'),
          phoneNumber: anyNamed('phoneNumber'),
          password: anyNamed('password'),
          role: anyNamed('role'),
        ),
      ).thenAnswer((_) async => true);

      // Act
      await tester.pumpWidget(
        TestHelpers.createTestApp(
          overrides: [
            authNotifierProvider.overrideWith((ref) => mockAuthNotifier),
          ],
          child: const RegisterScreen(),
        ),
      );

      // Fill form fields
      final nameFields = find.byType(BlazeTextFormField);
      await tester.enterText(nameFields.at(0), 'John Doe');
      await tester.enterText(nameFields.at(1), '<EMAIL>');
      await tester.enterText(nameFields.at(2), '**********');
      await tester.enterText(nameFields.at(3), 'password123');

      // Submit form
      await tester.tap(find.byType(FilledButton));
      await tester.pumpAndSettle();

      // Assert
      verify(
        mockAuthNotifier.register(
          name: 'John Doe',
          email: '<EMAIL>',
          phoneNumber: '**********',
          password: 'password123',
          role: 'PLAYER',
        ),
      ).called(1);
    });

    testWidgets('should navigate to OTP screen on successful registration', (
      tester,
    ) async {
      // Arrange
      String? navigatedRoute;
      final router = GoRouter(
        initialLocation: '/register',
        routes: [
          GoRoute(
            path: '/register',
            builder: (context, state) => const RegisterScreen(),
          ),
          GoRoute(
            path: '/otp-verification',
            builder: (context, state) {
              navigatedRoute = '/otp-verification';
              final phoneNumber =
                  state.uri.queryParameters['phoneNumber'] ?? '';
              final role = state.uri.queryParameters['role'] ?? 'PLAYER';
              return OtpVerificationScreen(
                phoneNumber: phoneNumber,
                role: role,
              );
            },
          ),
        ],
      );

      when(mockAuthNotifier.state).thenReturn(AuthState.initial());
      when(
        mockAuthNotifier.register(
          name: anyNamed('name'),
          email: anyNamed('email'),
          phoneNumber: anyNamed('phoneNumber'),
          password: anyNamed('password'),
          role: anyNamed('role'),
        ),
      ).thenAnswer((_) async => true);

      // Act
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            authNotifierProvider.overrideWith((ref) => mockAuthNotifier),
          ],
          child: MaterialApp.router(routerConfig: router),
        ),
      );

      // Fill form fields
      final nameFields = find.byType(BlazeTextFormField);
      await tester.enterText(nameFields.at(0), 'John Doe');
      await tester.enterText(nameFields.at(1), '<EMAIL>');
      await tester.enterText(nameFields.at(2), '**********');
      await tester.enterText(nameFields.at(3), 'password123');

      // Submit form
      await tester.tap(find.byType(FilledButton));
      await tester.pumpAndSettle();

      // Assert
      expect(navigatedRoute, equals('/otp-verification'));
      expect(find.byType(OtpVerificationScreen), findsOneWidget);
    });

    testWidgets('should show error snackbar on registration failure', (
      tester,
    ) async {
      // Arrange
      when(mockAuthNotifier.state).thenReturn(AuthState.initial());
      when(
        mockAuthNotifier.register(
          name: anyNamed('name'),
          email: anyNamed('email'),
          phoneNumber: anyNamed('phoneNumber'),
          password: anyNamed('password'),
          role: anyNamed('role'),
        ),
      ).thenAnswer((_) async => false);

      // Mock state change to error
      when(
        mockAuthNotifier.state,
      ).thenReturn(AuthState.error('Registration failed'));

      // Act
      await tester.pumpWidget(
        TestHelpers.createTestApp(
          overrides: [
            authNotifierProvider.overrideWith((ref) => mockAuthNotifier),
          ],
          child: const RegisterScreen(),
        ),
      );

      // Fill form fields
      final nameFields = find.byType(BlazeTextFormField);
      await tester.enterText(nameFields.at(0), 'John Doe');
      await tester.enterText(nameFields.at(1), '<EMAIL>');
      await tester.enterText(nameFields.at(2), '**********');
      await tester.enterText(nameFields.at(3), 'password123');

      // Submit form
      await tester.tap(find.byType(FilledButton));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Registration failed'), findsOneWidget);
      expect(find.byType(SnackBar), findsOneWidget);
    });

    testWidgets('should show loading state during registration', (
      tester,
    ) async {
      // Arrange
      when(mockAuthNotifier.state).thenReturn(AuthState.loading());

      // Act
      await tester.pumpWidget(
        TestHelpers.createTestApp(
          overrides: [
            authNotifierProvider.overrideWith((ref) => mockAuthNotifier),
          ],
          child: const RegisterScreen(),
        ),
      );

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Create account'), findsNothing);
    });

    testWidgets('should clear error when clearError is called', (tester) async {
      // Arrange
      when(mockAuthNotifier.state).thenReturn(AuthState.error('Some error'));

      // Act
      await tester.pumpWidget(
        TestHelpers.createTestApp(
          overrides: [
            authNotifierProvider.overrideWith((ref) => mockAuthNotifier),
          ],
          child: const RegisterScreen(),
        ),
      );

      // Verify clearError is called when form is submitted
      final nameFields = find.byType(BlazeTextFormField);
      await tester.enterText(nameFields.at(0), 'John Doe');
      await tester.enterText(nameFields.at(1), '<EMAIL>');
      await tester.enterText(nameFields.at(2), '**********');
      await tester.enterText(nameFields.at(3), 'password123');

      await tester.tap(find.byType(FilledButton));
      await tester.pumpAndSettle();

      // Assert
      verify(mockAuthNotifier.clearError()).called(1);
    });
  });
}
